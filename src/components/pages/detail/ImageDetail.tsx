import { use<PERSON><PERSON>, useAtomValue } from 'jotai'
import { PrinterStatus } from '@/stores/types'
import { VirtualInfo } from '@/apis/types'
import { useNavigate } from 'react-router-dom'
import useSwr from 'swr'
import {
  resultOrderAtom,
  printerEnableAtom,
  machineInfoAtom,
  selectedEventIdAtom,
  selectedImageFrameAtom,
  selectedEventDetailAtom,
  selectedThemeDetailAtom,
} from '@/stores'
import { useSearchParams } from 'react-router-dom'
import { useEffect, useState, useMemo, useRef } from 'react'
import { useAiTask } from '@/components/pages/photo/useAiTask'
import { useDevice } from '@/hooks/useDevice'
import { useTranslation } from 'react-i18next'
import { SendToEmailModal } from './SendToEmailModal'
import { DownloadModal } from './DownloadModal'
import { ShareModal } from './ShareModal'
import { PrintModal } from './PrintModal'
import { MyMirrorAiTask } from '@/stores/types'
import { AiTaskDetailType } from '@/graphqls/types'

import { publicPreLoadSourceObj } from '@/configs/source'
import _ajax from '@/utils/ajax'
import _api from '@/apis/maze.api'
import classNames from 'classnames'
import { useBridge } from '@/hooks/useBridge'
import { toast } from '@/components/ui/shad/use-toast'
import { SvgIcon } from '@/components/ui/SvgIcon'
import { MainImage } from '../result/MainImage'
import { CommonNavBar } from '@/components/ui/CommonNavBar'
import { Button } from '@/components/ui/shad/button'

export const ImageDetail = () => {
  const [curMaterialId, setCurMaterialId] = useState<
    { material_id: number; image_id: number }[] | null
  >(null)
  const [curVirtualInfo, setCurVirtualInfo] = useState<VirtualInfo>()
  // Flag to track if storeImageToMaze has been called for the current set of images
  const hasStoredImages = useRef(false)
  const [, setResultOrder] = useAtom(resultOrderAtom)
  const selectedThemeDetail = useAtomValue(selectedThemeDetailAtom)
  const [selectedEventId] = useAtom(selectedEventIdAtom)
  const [selectedEventDetail] = useAtom(selectedEventDetailAtom)
  const [selectedFrame] = useAtom(selectedImageFrameAtom)
  const { getDefaultDeviceInfo } = useDevice()
  const { downImage, tabbarVisible, tabbarTo } = useBridge()
  const { t } = useTranslation()
  const navigate = useNavigate()

  const [curOptType, setCurOptType] = useState<string | null>(null)
  const [activeImageIndex, setActiveImageIndex] = useState(0)
  // Store all selected images with their taskBaseIds
  const [selectedImageData, setSelectedImageData] = useState<{
    taskBaseIds: number[]
    imageIds: { [taskBaseId: number]: number[] }
    images: MyMirrorAiTask[]
  }>({
    taskBaseIds: [],
    imageIds: {},
    images: [],
  })

  const [searchParams] = useSearchParams()
  const [printerEnable] = useAtom(printerEnableAtom)
  const [machineInfo] = useAtom(machineInfoAtom)

  useEffect(() => {
    tabbarVisible({ visible: 0 })
  }, [])

  // 获取图片边框合成结果，轮询
  const { data: materiaDetail } = useSwr(
    curMaterialId ? [curMaterialId] : null,
    ([material_ids]) =>
      _ajax.post(_api.get_image, {
        material_ids: material_ids?.map(it => it.material_id),
      }),
    {
      refreshInterval: data => {
        const items = data?.data?.data
        const isAllImageCompleted =
          Array.isArray(items) &&
          items.length > 0 &&
          items.every((it: any) => it.url)
        if (isAllImageCompleted) {
          return 0 // Stop polling when all images are completed
        } else {
          return 3_000
        }
      },
    }
  )
  // Current active image
  const curImg = useMemo(() => {
    return selectedImageData.images[activeImageIndex] || null
  }, [selectedImageData.images, activeImageIndex])
  const curVideo = curImg?.video

  const curMaterialDetail = useMemo(() => {
    return materiaDetail?.data?.data?.find(
      (it: any) => it.wj_image_id === curImg?.id
    )
  }, [materiaDetail, curImg])
  const curMazeMaterialImageUrl = useMemo(() => {
    return curMaterialDetail?.url
  }, [curMaterialDetail])
  console.log(
    'curMazeMaterialImageUrl',
    curMaterialDetail,
    curImg,
    curMazeMaterialImageUrl
  )

  const hasPrinter = useMemo(() => {
    return (
      printerEnable &&
      (machineInfo?.printers?.some(
        it => it.printerStatus === PrinterStatus.AVAILABLE
      ) ||
        machineInfo?.printerStatus === PrinterStatus.AVAILABLE)
    )
  }, [printerEnable, machineInfo])
  // Format: /detail?images=taskId1:imageId1,imageId2;taskId2:imageId3,imageId4
  const imagesParam = searchParams.get('images')
  const imagesCount = useMemo(() => {
    if (!imagesParam) return 0
    // 计算所有图片的总数，而不是任务数量
    let totalCount = 0
    imagesParam.split(';').forEach(taskImagePair => {
      const [, imageIdsStr] = taskImagePair.split(':')
      if (imageIdsStr) {
        totalCount += imageIdsStr.split(',').length
      }
    })
    console.log('计算的图片总数:', totalCount, '来自参数:', imagesParam)
    return totalCount
  }, [imagesParam])

  // Parse the images parameter and fetch the images
  useEffect(() => {
    if (!imagesParam) return

    try {
      // Parse the images parameter
      // Format: taskId1:imageId1,imageId2;taskId2:imageId3,imageId4
      const taskImageMap: { [taskBaseId: number]: number[] } = {}
      const taskBaseIds: number[] = []

      imagesParam.split(';').forEach(taskImagePair => {
        const [taskId, imageIdsStr] = taskImagePair.split(':')
        const taskBaseId = Number(taskId)
        const imageIds = imageIdsStr.split(',').map(Number)

        taskImageMap[taskBaseId] = imageIds
        taskBaseIds.push(taskBaseId)
      })

      setSelectedImageData(prev => ({
        ...prev,
        taskBaseIds,
        imageIds: taskImageMap,
      }))

      // Stop any existing polling before starting new ones
      stopPollAiTaskStatus()

      // Create a single polling function that handles all taskBaseIds
      const pollAllTasks = () => {
        // Use Promise.all to fetch all tasks in parallel
        Promise.all(
          taskBaseIds.map(
            taskBaseId =>
              new Promise(resolve => {
                pollAiTaskStatus({
                  taskBaseId,
                  onProgress({ taskList }) {
                    updateImagesForTask(taskBaseId, taskList)
                    resolve(null)
                  },
                  onSuccess({ order, taskList }) {
                    updateImagesForTask(taskBaseId, taskList)
                    setResultOrder(order)
                    resolve(null)
                  },
                  onFail() {
                    console.error(
                      `Failed to fetch images for taskBaseId: ${taskBaseId}`
                    )
                    resolve(null)
                  },
                })
              })
          )
        ).catch(err => {
          console.error('Error polling tasks:', err)
        })
      }

      // Start polling
      pollAllTasks()
    } catch (error) {
      console.error('Error parsing images parameter:', error)
    }
  }, [imagesParam])

  // Update the images for a specific taskBaseId
  const updateImagesForTask = (
    taskBaseId: number,
    taskList: MyMirrorAiTask[]
  ) => {
    console.log(`=== 更新任务 ${taskBaseId} 的图片 ===`)
    console.log(
      '接收到的taskList:',
      taskList.map(t => ({ id: t.id, type: t.detailType }))
    )

    setSelectedImageData(prev => {
      // Filter the task list to only include DRAW type results
      const drawImages = taskList.filter(
        img => img.detailType === AiTaskDetailType.DRAW
      )
      // Get video results for later use
      const videoImages = taskList.filter(
        img => img.detailType === AiTaskDetailType.VIDEO
      )
      const resultImages = drawImages?.map(item => ({
        ...item,
        video: videoImages?.[0],
      }))

      return {
        ...prev,
        images: [...prev.images, ...resultImages],
      }
    })
  }

  console.log(
    'result',
    hasPrinter,
    printerEnable,
    machineInfo,
    curImg,
    selectedEventDetail,
    selectedImageData
  )

  const { pollAiTaskStatus, stopPollAiTaskStatus } = useAiTask()

  const handleToolbarClick = (op: string) => {
    if (op === 'download') {
      downImage({
        imageUrls: [curMazeMaterialImageUrl],
      })
      toast({
        description: t('保存成功'),
      })
      return
    }
    if (op === 'print') {
      _ajax.post(_api.record_print, { event_id: selectedEventId })
    }
    setCurOptType(op)
  }

  useEffect(() => {
    // 解决重复store问题
    console.log('检查是否需要存储图片:', {
      currentImagesLength: selectedImageData.images.length,
      expectedImagesCount: imagesCount,
      hasSelectedEventId: !!selectedEventId,
      hasStoredImages: hasStoredImages.current,
    })

    if (
      selectedImageData.images.length === imagesCount &&
      selectedEventId &&
      !hasStoredImages.current
    ) {
      console.log('开始存储图片到Maze:', selectedImageData)
      storeImageToMaze(selectedImageData.images, selectedEventId, selectedFrame)
      // Set flag to true to prevent multiple calls
      hasStoredImages.current = true
    }
  }, [selectedImageData, selectedEventId, selectedFrame, imagesCount])

  useEffect(() => {
    getVirtualDevice()
  }, [])

  const getVirtualDevice = async () => {
    const deviceInfo = await getDefaultDeviceInfo()
    const res = await _ajax.post(_api.get_virtual_device, {
      device_id: deviceInfo?.id,
    })
    if (res.data?.code === 200) {
      setCurVirtualInfo(res.data.data)
    }
  }

  const storeImageToMaze = async (
    images: any,
    event_id: number,
    selectedFrame: any
  ) => {
    console.log('selectedThemeDetail', selectedThemeDetail)

    // Check if all images have resultUrl
    const allImagesHaveUrls = images.every((it: any) => it.resultUrl)

    if (!allImagesHaveUrls) {
      console.log(
        'Not all images have resultUrl, will store them later when processed'
      )
      // Don't set hasStoredImages to true yet, as we'll need to store them again when all have URLs
      return
    }

    // Track retry attempts to prevent infinite retries
    const maxRetries = 3
    let retryCount = 0
    let success = false

    while (!success && retryCount < maxRetries) {
      try {
        const res = await _ajax.post(_api.store_image, {
          event_id,
          theme_id: selectedThemeDetail?.id,
          ...(selectedFrame?.id ? { frame_id: selectedFrame.id } : {}),
          images: images.map((it: any) => {
            const isVideo = !!it?.video
            return {
              image_id: it.id,
              url: isVideo ? it.video.resultUrl : it.resultUrl,
              cover_image_url: isVideo ? it.resultUrl : null,
              type: isVideo ? 'video' : 'image',
            }
          }),
        })
        const materialId = res.data?.data?.images
        if (materialId) {
          setCurMaterialId(materialId)
          console.log(
            'Images stored successfully with material IDs:',
            materialId
          )
          success = true
        } else {
          // If no material ID was returned but no error was thrown
          retryCount++
          console.warn(
            `No material ID returned, retry attempt ${retryCount}/${maxRetries}`
          )
          await new Promise(resolve => setTimeout(resolve, 1000)) // Wait 1 second before retrying
        }
      } catch (error) {
        retryCount++
        console.error(
          `Error storing images (attempt ${retryCount}/${maxRetries}):`,
          error
        )

        if (retryCount >= maxRetries) {
          console.error('Max retry attempts reached, giving up')
          // Only reset the flag after max retries to prevent excessive retries
          hasStoredImages.current = false
        } else {
          // Wait before retrying with exponential backoff
          await new Promise(resolve =>
            setTimeout(resolve, 1000 * Math.pow(2, retryCount - 1))
          )
        }
      }
    }
  }

  // Update images with processed URLs when materiaDetail changes
  useEffect(() => {
    if (materiaDetail?.data?.data && selectedImageData.images.length > 0) {
      // Define the type for processed images
      interface ProcessedImage {
        wj_image_id: number
        url: string
        id: number
        material_id: number
        cover_image_url: string
      }

      const processedImages = materiaDetail.data.data as ProcessedImage[]

      console.log('Processed images from materiaDetail:', processedImages)
      console.log('Current selectedImageData.images:', selectedImageData.images)

      // Create a map of current image URLs to avoid unnecessary updates
      const currentImageUrls = new Map(
        selectedImageData.images.map(img => [img.id, img.resultUrl])
      )

      // Check if we need to update any images - only update if URLs are different
      const imagesToUpdate: { id: number; url: string }[] = []

      processedImages.forEach((processedImg: ProcessedImage) => {
        const currentUrl = currentImageUrls.get(processedImg.wj_image_id)
        if (
          processedImg.cover_image_url && // Only consider images with URLs
          processedImg.url && // Only consider images with URLs
          processedImg.wj_image_id && // Only consider images with valid IDs
          currentUrl !== processedImg.url // Only update if URL is different
        ) {
          imagesToUpdate.push({
            id: processedImg.wj_image_id,
            url: processedImg.url,
          })
        }
      })

      const needsUpdate = imagesToUpdate.length > 0
      console.log(
        'Images need update:',
        needsUpdate,
        'Count:',
        imagesToUpdate.length
      )

      if (needsUpdate) {
        // Use a ref to track if we're currently updating to prevent circular updates
        const updatingRef = { current: true }

        setSelectedImageData(prev => {
          // Create a new array with updated image URLs
          const updatedImages = prev.images.map(img => {
            // Find if this image needs updating
            const updateInfo = imagesToUpdate.find(
              update => update.id === img.id
            )

            // If found and has a URL, update the image's resultUrl
            if (updateInfo) {
              return {
                ...img,
                resultUrl: updateInfo.url,
              }
            }
            // Otherwise return the original image
            return img
          })

          // After update is complete
          setTimeout(() => {
            updatingRef.current = false
          }, 0)

          return {
            ...prev,
            images: updatedImages,
          }
        })
      }
    }
    // Only depend on materiaDetail to prevent circular updates
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [materiaDetail])

  return (
    <>
      <div className="flex items-center justify-center gap-10 h-full w-full flex-col pt-12">
        <CommonNavBar
          title={t('详情')}
          onBack={() => {
            tabbarVisible({ visible: 1 })
            tabbarTo({ index: 'mine' })
            return true
          }}
        />
        <div className={classNames('flex-1 ')}>
          <MainImage curImg={curImg} className="h-full" />
        </div>
        <div className="w-full px-[20px] py-[12px] pb-[72px] flex gap-[12px]">
          <Button
            size="lg"
            className="maze-bg-gradient-btn flex-1 h-[56px] text-[16px] rounded-[150px] border-white border-[1px]"
            onClick={() => handleToolbarClick('download')}
          >
            <SvgIcon
              className="w-[24px] mr-[10px]"
              src={publicPreLoadSourceObj.detailDownload}
            />
            {t('下载')}
          </Button>
          {/* <Button
            size="lg"
            className="w-[56px] h-[56px] rounded-full bg-transparent border-white border-[1px]"
            onClick={() => handleToolbarClick('share')}
          >
            <SvgIcon src={publicPreLoadSourceObj.detailShare} alt="分享" />
          </Button> */}
          <Button
            size="lg"
            className="w-[56px] h-[56px] rounded-full bg-transparent border-white border-[1px]"
            onClick={() => navigate('/')}
          >
            <SvgIcon src={publicPreLoadSourceObj.shootRetry} />
          </Button>
        </div>
      </div>
      <SendToEmailModal
        open={curOptType === 'email'}
        setOpen={() => setCurOptType(null)}
        imgId={curMaterialId}
      />
      <DownloadModal
        open={curOptType === 'download'}
        setOpen={() => setCurOptType(null)}
        mazeImgUrl={
          curVirtualInfo?.status === 1 &&
          curVirtualInfo?.expire_ts * 1000 > +new Date()
            ? `${window.location.href}&virtual_uid=${curVirtualInfo?.uid}&navigate=noop`
            : curVideo
              ? curVideo.resultUrl
              : curMazeMaterialImageUrl
        }
      />
      <PrintModal
        open={curOptType === 'print'}
        setOpen={() => setCurOptType(null)}
        curImg={curImg}
        mazeImgUrl={curMazeMaterialImageUrl}
      />
      <ShareModal
        open={curOptType === 'share'}
        setOpen={() => setCurOptType(null)}
        mazeImgUrl={curMazeMaterialImageUrl}
      />
    </>
  )
}
